<template>
  <div id="app">
    <!-- 全屏背景效果 -->
    <div class="full-bg" :style="backgroundImageUrl ? { backgroundImage: 'url(' + backgroundImageUrl + ')' } : {}"></div>

    <div class="main-container">
      <div class="col-xs-12 col-sm-10 col-md-8 col-lg-5 center-block" style="float: none;">

      <!-- 弹出公告模态框 -->
      <div class="modal fade" align="left" id="myModal" tabindex="-1" role="dialog" aria-labelledby="myModalLabel" aria-hidden="true" v-show="showModal">
        <div class="modal-dialog">
          <div class="modal-content">
            <div class="modal-header-tabs">
              <button type="button" class="close" @click="showModal = false">
                <span aria-hidden="true">×</span>
                <span class="sr-only">Close</span>
              </button>
              <h4 class="modal-title" id="myModalLabel">依思商城</h4>
            </div>
            <div class="modal-body">
              <!-- 公告内容 -->
            </div>
            <div class="modal-footer">
              <button type="button" class="btn btn-default" @click="showModal = false">知道啦</button>
            </div>
          </div>
        </div>
      </div>

      <!-- 平台公告模态框 -->
      <div class="modal fade" align="left" id="anounce" tabindex="-1" role="dialog" aria-labelledby="myModalLabel" aria-hidden="true" v-show="showAnnounceModal" style="display: none;">
        <div class="modal-dialog">
          <div class="modal-content">
            <div class="modal-header" style="background:linear-gradient(120deg, #31B404 0%, #D7DF01 100%);">
              <button type="button" class="close" @click="showAnnounceModal = false">
                <span aria-hidden="true">×</span>
                <span class="sr-only">Close</span>
              </button>
              <div class="text-center">
                <h4 class="modal-title" id="myModalLabel">
                  <b><span style="color:#fff">依思商城</span></b>
                </h4>
              </div>
            </div>
            <div class="widget flat radius-bordered">
              <div class="widget-header bordered-top bordered-themesecondary">
                <div class="modal-body">
                  <p>
                    <li class="list-group-item">
                      <span class="btn btn-danger btn-xs">1</span> 售后问题可直接联系平台在线QQ客服
                    </li>
                    <li class="list-group-item">
                      <span class="btn btn-success btn-xs">2</span> 下单之前请一定要看完该商品的注意事项再进行下单！
                    </li>
                    <li class="list-group-item">
                      <span class="btn btn-info btn-xs">3</span> 所有业务全部恢复，都可以正常下单，欢迎尝试
                    </li>
                    <li class="list-group-item">
                      <span class="btn btn-warning btn-xs">4</span> 温馨提示：请勿重复下单哦！必须要等待前面任务订单完成才可以下单！
                    </li>
                    <li class="list-group-item">
                      <span class="btn btn-primary btn-xs">5</span>
                      <a href="./user/regsite.php">价格贵？不怕，点击0元搭建，在后台超低价下单！</a>
                    </li>
                    <div class="btn-group btn-group-justified">
                      <a target="_blank" class="btn btn-info" href="http://wpa.qq.com/msgrd?v=3&uin=123456&site=qq&menu=yes">
                        <i class="fa fa-qq"></i> 联系客服
                      </a>
                      <a target="_blank" class="btn btn-warning" href="http://qun.qq.com/join.html">
                        <i class="fa fa-users"></i> 官方Q群
                      </a>
                      <a target="_blank" class="btn btn-danger" href="./">
                        <i class="fa fa-cloud-download"></i> APP下载
                      </a>
                    </div>
                  </p>
                </div>
              </div>
            </div>
            <div class="modal-footer">
              <button type="button" class="btn btn-default" @click="showAnnounceModal = false">我明白了</button>
            </div>
          </div>
        </div>
      </div>

      <!-- Logo区域 -->
      <div class="widget">
        <div class="widget-content themed-background-flat text-center logo-header">
          <div class="logo-avatar-container">
            <div class="logo-avatar">
              <img src="https://q.qlogo.cn/headimg_dl?dst_uin=107766441&spec=640&img_type=jpg" alt="头像" class="avatar-img">
            </div>
          </div>
        </div>

        <div class="text-center logo-title">
          <h2>
            <span class="brand-link">
              <b>依思商城</b>
            </span>
          </h2>
          <p class="brand-subtitle">专业的数字商品交易平台</p>
        </div>

        <!-- Logo下面按钮 -->
        <div class="widget-content text-center action-buttons">
          <div class="btn-group btn-group-justified modern-btn-group">
            <div class="btn-group">
              <a class="btn btn-modern btn-announce" @click="showAnnounceModal = true">
                <i class="fa fa-bullhorn"></i>
                <span>公告</span>
              </a>
            </div>
            <div class="btn-group">
              <a class="btn btn-modern btn-service" @click="showCustomerServiceModal = true">
                <i class="fa fa-qq"></i>
                <span>客服</span>
              </a>
            </div>
            <div class="btn-group">
              <a class="btn btn-modern btn-login" href="user/login.php">
                <i class="fa fa-users"></i>
                <span>登录</span>
              </a>
            </div>
          </div>
        </div>
      </div>

      <!-- Tab导航区域 -->
      <div class="block full2 modern-tabs">
        <div class="block-title">
          <ul class="nav nav-tabs modern-nav-tabs" data-toggle="tabs">
            <li class="nav-item" :class="{ active: activeTab === 'shop' }">
              <a href="#shop" @click="activeTab = 'shop'" class="nav-link">
                <i class="fa fa-shopping-cart"></i>
                <span>下单</span>
              </a>
            </li>
            <li class="nav-item" :class="{ active: activeTab === 'search' }">
              <a href="#search" @click="activeTab = 'search'" class="nav-link">
                <i class="fa fa-search"></i>
                <span>查询</span>
              </a>
            </li>
            <li class="nav-item" :class="{ active: activeTab === 'substation' }">
              <a href="#Substation" @click="activeTab = 'substation'" class="nav-link nav-link-special">
                <i class="fa fa-location-arrow fa-spin"></i>
                <span>分站</span>
              </a>
            </li>
            <li class="nav-item" :class="{ active: activeTab === 'more' }">
              <a href="#more" @click="activeTab = 'more'" class="nav-link">
                <i class="fa fa-list"></i>
                <span>更多</span>
              </a>
            </li>
          </ul>
        </div>

        <!-- Tab内容区域 -->
        <div class="tab-content">
          <!-- 在线下单 -->
          <div class="tab-pane" :class="{ active: activeTab === 'shop' }" id="shop">
            <div class="text-center">
              <div class="shuaibi-tip animated tada text-center">
                <i class="fa fa-heart text-danger"></i>
                <b>[[ currentDateTime ]]</b>
              </div>
            </div>

            <div id="goodTypeContents" class="modern-order-form">
              <div class="form-group" id="display_searchBar">
                <div class="input-group">
                  <div class="input-group-addon">搜索商品</div>
                  <input type="text" id="searchkw" class="form-control" placeholder="搜索商品" v-model="searchKeyword" @keydown.enter="doSearch"/>
                  <div class="input-group-addon">
                    <i class="fa fa-search onclick" title="搜索" id="doSearch" @click="doSearch"></i>
                  </div>
                </div>
              </div>

              <div class="form-group" id="display_selectclass">
                <div class="input-group">
                  <div class="input-group-addon">选择分类</div>
                  <select name="tid" id="cid" class="form-control" v-model="selectedCategory" @change="getProductsByCategory">
                    <option value="0">请选择分类</option>
                  </select>
                </div>
              </div>



              <div class="form-group" id="display_price" style="display:none;text-align:center;color:#4169E1;font-weight:bold">
                <div class="input-group">
                  <div class="input-group-addon">商品价格</div>
                  <input type="text" name="need" id="need" class="form-control" style="text-align:center;color:#4169E1;font-weight:bold" disabled v-model="productPrice"/>
                </div>
              </div>

              <div class="form-group" id="display_left" style="display:none;">
                <div class="input-group">
                  <div class="input-group-addon">库存数量</div>
                  <input type="text" name="leftcount" id="leftcount" class="form-control" disabled v-model="stockCount"/>
                </div>
              </div>

              <div class="form-group" id="display_num" style="display:none;">
                <div class="input-group">
                  <div class="input-group-addon">下单份数</div>
                  <span class="input-group-btn">
                    <input id="num_min" type="button" class="btn btn-info" style="border-radius: 0px;" value="━" @click="decreaseQuantity">
                  </span>
                  <input id="num" name="num" class="form-control" type="number" min="1" v-model="orderQuantity"/>
                  <span class="input-group-btn">
                    <input id="num_add" type="button" class="btn btn-info" style="border-radius: 0px;" value="✚" @click="increaseQuantity">
                  </span>
                </div>
              </div>

              <div id="inputsname"></div>

              <div id="alert_frame" class="alert alert-success animated rubberBand" style="display:none;background: linear-gradient(to right,#71D7A2,#5ED1D7);font-weight: bold;color:white;" v-show="showAlert" v-html="alertMessage"></div>

              <div class="form-group">
                <a type="submit" id="submit_buy" class="btn btn-block btn-primary" @click="buyNow">立即购买</a>
              </div>


            </div>
          </div>

          <!-- 查询订单 -->
          <div class="tab-pane" :class="{ active: activeTab === 'search' }" id="search">
            <!-- 客服信息卡片 -->
            <div class="customer-service-card">
              <div class="service-avatar">
                <img src="//q4.qlogo.cn/headimg_dl?dst_uin=123456789&spec=100" alt="客服头像" class="avatar-image">
                <div class="online-status"></div>
              </div>
              <div class="service-info">
                <div class="service-title">
                  <i class="fa fa-user-circle"></i>
                  <span>专属客服</span>
                </div>
                <div class="service-details">
                  <div class="contact-item">
                    <i class="fa fa-qq"></i>
                    <span>123456789</span>
                  </div>
                  <div class="service-desc">售后订单问题请联系客服</div>
                </div>
              </div>
              <div class="service-action">
                <a href="#lxkf" @click="showCustomerServiceModal = true" class="contact-btn">
                  <i class="fa fa-comments"></i>
                  <span>联系客服</span>
                </a>
              </div>
            </div>

            <br>


            <!-- 现代化搜索框 -->
            <div class="search-container">
              <div class="search-input-wrapper">
                <div class="search-icon">
                  <i class="fa fa-search"></i>
                </div>
                <input
                  type="text"
                  v-model="searchQuery"
                  class="modern-search-input"
                  placeholder="请输入订单号查询"
                  @keydown.enter="submitQuery"
                >
              </div>

              <button class="modern-search-btn" @click="submitQuery">
                <i class="fa fa-search"></i>
                <span>立即查询</span>
              </button>
            </div>

            <br>
            <!-- 查询结果区域 -->
            <div class="query-results" v-show="showQueryResult">
              <!-- 订单卡片列表 -->
              <div class="order-cards">
                <div class="order-card" v-for="order in queryResults" :key="order.id">
                  <div class="order-header">
                    <div class="order-id">
                      <i class="fa fa-file-text-o"></i>
                      <span>[[ order.id ]]</span>
                    </div>
                    <div class="order-status" :class="'status-' + order.status">
                      <i class="fa" :class="{
                        'fa-check-circle': order.status === 'completed',
                        'fa-clock-o': order.status === 'processing',
                        'fa-exclamation-triangle': order.status === 'pending',
                        'fa-times-circle': order.status === 'failed'
                      }"></i>
                      <span>[[ order.statusText ]]</span>
                    </div>
                  </div>

                  <div class="order-content">
                    <div class="order-info">
                      <div class="info-item">
                        <label>商品名称</label>
                        <span>[[ order.productName ]]</span>
                      </div>
                      <div class="info-item">
                        <label>下单账号</label>
                        <span>[[ order.account ]]</span>
                      </div>
                      <div class="info-row">
                        <div class="info-item">
                          <label>数量</label>
                          <span>[[ order.quantity ]]</span>
                        </div>
                        <div class="info-item">
                          <label>金额</label>
                          <span class="amount">¥[[ order.amount ]]</span>
                        </div>
                      </div>
                      <div class="info-item">
                        <label>创建时间</label>
                        <span>[[ order.createTime ]]</span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              <!-- 空状态 -->
              <div class="empty-state" v-if="queryResults.length === 0">
                <div class="empty-icon">
                  <i class="fa fa-search"></i>
                </div>
                <div class="empty-text">
                  <h4>暂无查询结果</h4>
                  <p>请检查订单号是否正确，或尝试留空查询最新订单</p>
                </div>
              </div>
            </div>
          </div>

          <!-- 开通分站 -->
          <div class="tab-pane animation-fadeInQuick2" :class="{ active: activeTab === 'substation' }" id="Substation">
            <table class="table table-borderless table-pricing">
              <tbody>
                <tr class="active">
                  <td class="btn-effect-ripple" style="overflow: hidden; position: relative;width: 100%; height: 8em;display: block;color: white;margin: auto;background-color: lightskyblue;">
                    <span class="btn-ripple animate" style="height: 546px; width: 546px; top: -212.8px; left: 56.4px;"></span>
                    <h3 style="width:100%;font-size: 1.6em;">
                    </h3>
                    <h3 style="width:100%;font-size: 1.6em;">
                      <i class="fa fa-user-o fa-fw" style="margin-top: 0.7em;"></i><strong>入门级</strong> /
                      <i class="fa fa-user-circle-o fa-fw"></i><strong>旗舰级</strong>
                    </h3>
                    <span style="width: 100%;text-align: center;margin-top: 0.8em;font-size: 1.1em;display: block;">10元 / 20元</span>
                  </td>
                </tr>
                <tr>
                  <td>一模一样的独立网站</td>
                </tr>
                <tr>
                  <td>站长后台和超低秘价</td>
                </tr>
                <tr>
                  <td>余额提成满10元提现</td>
                </tr>
                <tr>
                  <td><strong>旗舰级可以吃下级分站提成</strong></td>
                </tr>
                <tr class="active">
                  <td>
                    <a href="#userjs" @click="showVersionModal = true" class="btn btn-effect-ripple btn-info" style="overflow: hidden; position: relative;">
                      <i class="fa fa-align-justify"></i>
                      <span class="btn-ripple animate" style="height: 100px; width: 100px; top: -24.8px; left: 11.05px;"></span> 版本介绍
                    </a>
                    <a href="user/regsite.php" target="_blank" class="btn btn-effect-ripple btn-danger" style="overflow: hidden; position: relative;">
                      <i class="fa fa-arrow-right"></i> 马上开通
                    </a>
                  </td>
                </tr>
              </tbody>
            </table>
          </div>

          <!-- 更多按钮 -->
          <div class="tab-pane" :class="{ active: activeTab === 'more' }" id="more">
            <div class="row">
              <div class="col-sm-6">
                <a href="./user/" target="_blank" class="widget">
                  <div class="widget-content themed-background-info text-right clearfix" style="color: #fff;">
                    <div class="widget-icon pull-left">
                      <i class="fa fa-certificate"></i>
                    </div>
                    <h2 class="widget-heading h3">
                      <strong>分站后台</strong>
                    </h2>
                    <span>登录分站后台</span>
                  </div>
                </a>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 数据统计 -->
      <div class="panel panel-primary stats-panel">
        <div class="panel-heading stats-header">
          <h3 class="panel-title">
            <i class="fa fa-bar-chart-o"></i>
            <span>数据统计</span>
          </h3>
        </div>
        <div class="stats-grid">
          <div class="stats-item">
            <div class="stats-number">[[ statistics.operatingDays ]]</div>
            <div class="stats-unit">天</div>
            <div class="stats-icon"><i class="fa fa-shield"></i></div>
            <div class="stats-label">安全运营</div>
          </div>
          <div class="stats-item">
            <div class="stats-number">[[ statistics.totalAmount ]]</div>
            <div class="stats-unit">元</div>
            <div class="stats-icon"><i class="fa fa-shopping-cart"></i></div>
            <div class="stats-label">交易总数</div>
          </div>
          <div class="stats-item">
            <div class="stats-number">[[ statistics.totalOrders ]]</div>
            <div class="stats-unit">笔</div>
            <div class="stats-icon"><i class="fa fa-check-square-o"></i></div>
            <div class="stats-label">订单总数</div>
          </div>
          <div class="stats-item">
            <div class="stats-number">[[ statistics.agentSites ]]</div>
            <div class="stats-unit">个</div>
            <div class="stats-icon"><i class="fa fa-sitemap"></i></div>
            <div class="stats-label">代理分站</div>
          </div>
          <div class="stats-item">
            <div class="stats-number">[[ statistics.todayAmount ]]</div>
            <div class="stats-unit">元</div>
            <div class="stats-icon"><i class="fa fa-pie-chart"></i></div>
            <div class="stats-label">今日交易</div>
          </div>
          <div class="stats-item">
            <div class="stats-number">[[ statistics.todayOrders ]]</div>
            <div class="stats-unit">笔</div>
            <div class="stats-icon"><i class="fa fa-check-square"></i></div>
            <div class="stats-label">今日订单</div>
          </div>
        </div>
      </div>

      <!-- 底部导航 -->
      <div class="panel panel-default">
        <div class="text-center">
          <div class="panel-body">
            <span style="font-weight:bold">依思商城 <i class="fa fa-heart text-danger"></i> [[ currentYear ]] | </span>
            <a href="./"><span style="font-weight:bold">[[ currentDomain ]]</span></a><br/>
          </div>
        </div>
      </div>
    </div>
  </div>

    <!-- 客服介绍模态框 -->
    <div class="modal fade col-xs-12" align="left" id="lxkf" tabindex="-1" role="dialog" aria-labelledby="myModalLabel" aria-hidden="true" v-show="showCustomerServiceModal">
      <br><br>
      <div class="modal-dialog panel panel-primary animation-fadeInQuick2">
        <div class="modal-content">
          <div class="list-group-item reed" style="background:linear-gradient(120deg, #5ED1D7 10%, #71D7A2 90%);">
            <button type="button" class="close" @click="showCustomerServiceModal = false">
              <span aria-hidden="true">×</span>
              <span class="sr-only">Close</span>
            </button>
            <div class="text-center">
              <h4 class="modal-title" id="myModalLabel">
                <b><span style="color:#fff">客服与帮助</span></b>
              </h4>
            </div>
          </div>
          <div class="modal-body" id="accordion">
            <div class="panel panel-default" style="margin-bottom: 6px;">
              <div class="panel-heading">
                <h4 class="panel-title">
                  <a data-toggle="collapse" data-parent="#accordion" href="#collapseOne">为什么订单显示已完成了却一直没到账？</a>
                </h4>
              </div>
              <div id="collapseOne" class="panel-collapse in" style="height: auto;">
                <div class="panel-body">
                  订单显示（已完成）就证明已经提交到服务器内！<br>
                  如果长时间没到账请联系客服处理！<br>
                  订单长时间显示（待处理）请联系客服！
                </div>
              </div>
            </div>
            <div class="panel panel-default" style="margin-bottom: 6px;">
              <div class="panel-heading">
                <h4 class="panel-title">
                  <a data-toggle="collapse" data-parent="#accordion" href="#collapseTwo" class="collapsed">商品什么时候到账？</a>
                </h4>
              </div>
              <div id="collapseTwo" class="panel-collapse collapse" style="height: 0px;">
                <div class="panel-body">
                  请参考商品简介里面，有关于到账时间的说明。
                </div>
              </div>
            </div>
            <div class="panel panel-default" style="margin-bottom: 6px;">
              <div class="panel-heading">
                <h4 class="panel-title">
                  <a data-toggle="collapse" data-parent="#accordion" href="#collapseThree" class="collapsed">卡密没有发送我的邮箱？</a>
                </h4>
              </div>
              <div id="collapseThree" class="panel-collapse collapse" style="height: 0px;">
                <div class="panel-body">
                  没有收到请检查自己邮箱的垃圾箱！也可以去查单区：输入自己下单时填写的邮箱进行查单。<br>
                  查询到订单后点击（详细）就可以看到自己购买的卡密！
                </div>
              </div>
            </div>
            <div class="panel panel-default" style="margin-bottom: 6px;">
              <div class="panel-heading">
                <h4 class="panel-title">
                  <a data-toggle="collapse" data-parent="#accordion" href="#collapseFourth" class="collapsed">已付款了没有查询到我订单？</a>
                </h4>
              </div>
              <div id="collapseFourth" class="panel-collapse collapse" style="height: 0px;">
                <div class="panel-body" style="margin-bottom: 6px;">
                  联系客服处理，请提供（付款详细记录截图）（下单商品名称）（下单账号）<br>
                  直接把三个信息发给客服，然后等待客服回复处理（请不要发抖动窗口或者QQ电话）！
                </div>
              </div>
            </div>
            <ul class="list-group" style="margin-bottom: 0px;">
              <li class="list-group-item">
                <div class="media">
                  <span class="pull-left thumb-sm">
                    <img src="//q4.qlogo.cn/headimg_dl?dst_uin=123456789&spec=100" alt="..." class="img-circle img-thumbnail img-avatar">
                  </span>
                  <div class="pull-right push-15-t">
                    <a href="http://wpa.qq.com/msgrd?v=3&uin=123456789&site=qq&menu=yes" target="_blank" class="btn btn-sm btn-info">联系</a>
                  </div>
                  <div class="pull-left push-10-t">
                    <div class="font-w600 push-5">订单售后客服</div>
                    <div class="text-muted"><b>QQ：123456789</b></div>
                  </div>
                </div>
              </li>
              <li class="list-group-item">
                想要快速回答你的问题就请把问题描述讲清楚!<br>
                下单账号+业务名称+问题，直奔主题，按顺序回复!<br>
                有问题直接留言，请勿抖动语音否则直接无视。<br>
              </li>
            </ul>
          </div>
          <div class="modal-footer">
            <button type="button" class="btn btn-default" @click="showCustomerServiceModal = false">关闭</button>
          </div>
        </div>
      </div>
    </div>

    <!-- 分站介绍模态框 -->
    <div class="modal fade" align="left" id="userjs" tabindex="-1" role="dialog" aria-labelledby="myModalLabel" aria-hidden="true" v-show="showVersionModal" style="display: none;">
      <div class="modal-dialog">
        <div class="modal-content">
          <div class="list-group-item reed" style="background:linear-gradient(120deg, #FE2EF7 10%, #71D7A2 90%);">
            <button type="button" class="close" @click="showVersionModal = false">
              <span aria-hidden="true">×</span>
              <span class="sr-only">Close</span>
            </button>
            <div class="text-center">
              <h4 class="modal-title" id="myModalLabel">
                <b><span style="color:#fff">版本介绍</span></b>
              </h4>
            </div>
          </div>
          <div class="modal-body">
            <div class="table-responsive">
              <table class="table table-borderless table-vcenter">
                <thead>
                  <tr>
                    <th style="width: 100px;">功能</th>
                    <th class="text-center" style="width: 20px;">普及版/专业版</th>
                  </tr>
                </thead>
                <tbody>
                  <tr class="active">
                    <td>独立网站/专属后台</td>
                    <td class="text-center">
                      <span class="btn btn-effect-ripple btn-xs btn-success" style="overflow: hidden; position: relative;">
                        <i class="fa fa-check"></i>
                      </span>
                      <span class="btn btn-effect-ripple btn-xs btn-success" style="overflow: hidden; position: relative;">
                        <i class="fa fa-check"></i>
                      </span>
                    </td>
                  </tr>
                  <tr class="">
                    <td>低价拿货/调整价格</td>
                    <td class="text-center">
                      <span class="btn btn-effect-ripple btn-xs btn-success" style="overflow: hidden; position: relative;">
                        <i class="fa fa-check"></i>
                      </span>
                      <span class="btn btn-effect-ripple btn-xs btn-success" style="overflow: hidden; position: relative;">
                        <i class="fa fa-check"></i>
                      </span>
                    </td>
                  </tr>
                  <tr class="info">
                    <td>搭建分站/管理分站</td>
                    <td class="text-center">
                      <span class="btn btn-effect-ripple btn-xs btn-danger" style="overflow: hidden; position: relative;">
                        <i class="fa fa-close"></i>
                      </span>
                      <span class="btn btn-effect-ripple btn-xs btn-success" style="overflow: hidden; position: relative;">
                        <i class="fa fa-check"></i>
                      </span>
                    </td>
                  </tr>
                  <tr class="">
                    <td>超低密价/高额提成</td>
                    <td class="text-center">
                      <span class="btn btn-effect-ripple btn-xs btn-danger" style="overflow: hidden; position: relative;">
                        <i class="fa fa-close"></i>
                      </span>
                      <span class="btn btn-effect-ripple btn-xs btn-success" style="overflow: hidden; position: relative;">
                        <i class="fa fa-check"></i>
                      </span>
                    </td>
                  </tr>
                  <tr class="danger">
                    <td>赠送专属APP</td>
                    <td class="text-center">
                      <span class="btn btn-effect-ripple btn-xs btn-danger" style="overflow: hidden; position: relative;">
                        <i class="fa fa-close"></i>
                      </span>
                      <span class="btn btn-effect-ripple btn-xs btn-success" style="overflow: hidden; position: relative;">
                        <i class="fa fa-check"></i>
                      </span>
                    </td>
                  </tr>
                </tbody>
              </table>
            </div>
          </div>
          <div class="modal-footer">
            <button type="button" class="btn btn-default" @click="showVersionModal = false">关闭</button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'RainbowShop',
  data() {
    return {
      // 模态框控制
      showModal: false,
      showAnnounceModal: false,
      showCustomerServiceModal: false,
      showVersionModal: false,

      // Tab控制
      activeTab: 'shop',

      // 下单相关
      searchKeyword: '',
      selectedCategory: '0',
      productPrice: '',
      stockCount: '',
      orderQuantity: 1,
      showAlert: false,
      alertMessage: '',

      // 查询相关
      searchQuery: '',
      showQueryResult: false,
      queryResults: [],

      // 统计数据
      statistics: {
        operatingDays: '9',
        totalAmount: '0',
        totalOrders: '0',
        agentSites: '0',
        todayAmount: '0',
        todayOrders: '0'
      },

      // 动态信息
      currentYear: new Date().getFullYear(),
      currentDomain: window.location.hostname,
      currentDateTime: '',

      // 背景图片
      backgroundImageUrl: ''
    }
  },
  methods: {
    // 搜索商品
    doSearch() {
      console.log('搜索商品:', this.searchKeyword);
      // 实现搜索逻辑
    },

    // 根据分类获取商品列表
    getProductsByCategory() {
      console.log('根据分类获取商品:', this.selectedCategory);
      // 实现根据分类获取商品列表的逻辑
      // 选择分类后可以直接显示该分类下的商品信息或进行其他操作
    },

    // 增加数量
    increaseQuantity() {
      this.orderQuantity++;
    },

    // 减少数量
    decreaseQuantity() {
      if (this.orderQuantity > 1) {
        this.orderQuantity--;
      }
    },

    // 立即购买
    buyNow() {
      console.log('立即购买');
      // 实现购买逻辑
    },

    // 提交查询
    submitQuery() {
      // 检查输入框是否为空
      if (!this.searchQuery || this.searchQuery.trim() === '') {
        alert('请输入订单号');
        return;
      }

      console.log('查询订单:', this.searchQuery);
      this.showQueryResult = true;
      // 模拟查询结果
      this.queryResults = [
        {
          id: 'ORD20250731001',
          account: '<EMAIL>',
          productName: 'QQ会员充值',
          quantity: 1,
          createTime: '2025-07-31 14:30:25',
          status: 'completed',
          statusText: '已完成',
          amount: '15.00'
        }
      ];
    },

    // 获取背景图片
    fetchBackgroundImage() {
      try {
        // 直接使用接口URL作为背景图片，添加时间戳确保每次获取新图片
        const timestamp = new Date().getTime();
        this.backgroundImageUrl = `https://imgapi.xl0408.top/index.php?t=${timestamp}`;
      } catch (error) {
        // 使用默认背景
        this.backgroundImageUrl = '';
      }
    },

    // 刷新背景图片
    refreshBackgroundImage() {
      this.fetchBackgroundImage();
    },

    // 更新当前日期时间
    updateCurrentDateTime() {
      const now = new Date();
      const year = now.getFullYear();
      const month = String(now.getMonth() + 1).padStart(2, '0');
      const day = String(now.getDate()).padStart(2, '0');
      const hours = String(now.getHours()).padStart(2, '0');
      const minutes = String(now.getMinutes()).padStart(2, '0');
      const seconds = String(now.getSeconds()).padStart(2, '0');

      this.currentDateTime = `［${year}年${month}月${day}日 ${hours}:${minutes}:${seconds}］当前系统时间`;
    }
  },

  mounted() {
    // 确保获取最新的年份和域名
    this.currentYear = new Date().getFullYear();
    this.currentDomain = window.location.hostname || window.location.host;

    // 初始化当前日期时间
    this.updateCurrentDateTime();
    // 每秒更新一次时间
    setInterval(() => {
      this.updateCurrentDateTime();
    }, 1000);

    // 获取背景图片
    this.fetchBackgroundImage();
  }
}
</script>

<style>
/* 导入外部CSS框架 */
@import url('//cdn.staticfile.org/twitter-bootstrap/3.3.7/css/bootstrap.min.css');
@import url('//cdn.staticfile.org/font-awesome/4.7.0/css/font-awesome.min.css');

/* 全局样式 */
body {
  background: #ffffff;
  font-family: "Lato", "Helvetica Neue", Helvetica, Arial, sans-serif;
  color: #454e59;
  margin: 0;
  padding: 0;
}

/* 主容器样式 */
.main-container {
  min-height: 100vh;
  padding: 10px 0;
  background: rgba(248, 249, 250, 0.3);
  backdrop-filter: blur(1px);
}

/* 自定义提示框样式 */
.shuaibi-tip {
  background: #dc3545;
  color: white;
  margin: 8px 0;
  padding: 8px;
  border-radius: 4px;
  font-size: 14px;
  height: auto;
  min-height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
  text-align: center;
}

.shuaibi-tip .label-danger {
  background: rgba(255, 255, 255, 0.2);
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 12px;
}

/* 全屏背景效果 */
.full-bg {
  min-height: 100vh;
  width: 100%;
  position: fixed;
  top: 0;
  left: 0;
  background-color: #ffffff;
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
  background-attachment: fixed;
  z-index: -1;
}

/* 背景图片上的轻微白色遮罩 */
.full-bg::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(255, 255, 255, 0.5);
  pointer-events: none;
}

/* Widget组件样式 */
.widget {
  background-color: rgba(255, 255, 255, 0.9);
  margin-bottom: 12px;
  position: relative;
  border-radius: 6px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.15);
  border: 1px solid rgba(233, 236, 239, 0.8);
}

.widget-content {
  padding: 12px;
}

/* Logo区域样式 */
.logo-header {
  background: #667eea;
  padding: 15px;
}

.logo-avatar {
  width: 80px;
  height: 80px;
  background: rgba(255, 255, 255, 0.15);
  border-radius: 50px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto;
  color: white;
  overflow: hidden;
}

.avatar-img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  border-radius: 50px;
}

.logo-title {
  padding: 12px;
  background: white;
}

.brand-link {
  color: #333;
  text-decoration: none;
  font-size: 18px;
}

.brand-link:hover {
  color: #667eea;
  text-decoration: none;
}

.brand-subtitle {
  color: #6c757d;
  font-size: 12px;
  margin: 3px 0 0 0;
}

/* 现代化按钮样式 */
.action-buttons {
  padding: 8px;
  background: #f8f9fa;
}

.modern-btn-group {
  display: flex;
  gap: 6px;
}

.modern-btn-group .btn-group {
  flex: 1;
}

.btn-modern {
  background: white;
  border: 1px solid #dee2e6;
  border-radius: 4px;
  padding: 6px 6px;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 2px;
  text-decoration: none;
  color: #495057;
  font-size: 14px;
  width: 100%;
  height: 48px;
  justify-content: center;
}

.btn-modern:hover {
  background: #667eea;
  color: white;
  text-decoration: none;
}

.btn-modern i {
  font-size: 16px;
}

/* Block组件样式 */
.block {
  margin: 0 0 12px;
  background-color: rgba(255, 255, 255, 0.9);
  border-radius: 6px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.15);
  border: 1px solid rgba(233, 236, 239, 0.8);
  overflow: hidden;
}

.block.full2 {
  padding: 0;
}

.block-title {
  margin: 0;
  border-bottom: none;
  background: #f8f9fa;
  border-radius: 6px 6px 0 0;
}

.modern-nav-tabs {
  padding: 0;
  margin: 0;
  border-bottom: none;
  display: flex;
  background: #f8f9fa;
  border-radius: 6px 6px 0 0;
}

.modern-nav-tabs .nav-item {
  flex: 1;
  list-style: none;
}

.modern-nav-tabs .nav-link {
  border: none;
  padding: 8px 8px;
  margin: 0;
  border-radius: 0;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 2px;
  text-decoration: none;
  color: #6c757d;
  font-size: 14px;
  background: transparent;
  height: 48px;
  justify-content: center;
}

.modern-nav-tabs .nav-link:hover {
  background: #e9ecef;
  color: #495057;
  text-decoration: none;
}

.modern-nav-tabs .nav-link i {
  font-size: 16px;
}

.modern-nav-tabs .nav-item.active .nav-link {
  background: #667eea;
  color: white;
}

.nav-link-special {
  color: #dc3545 !important;
}

.modern-nav-tabs .nav-item.active .nav-link-special {
  background: #dc3545 !important;
  color: white !important;
}

/* Tab内容样式 */
.tab-content {
  padding: 8px;
  background: rgba(255, 255, 255, 0.95);
  border-radius: 0 0 6px 6px;
  min-height: 200px;
}

/* 现代化下单表单样式 */
.modern-order-form {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 12px;
  padding: 16px;
  margin: 8px 0;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(233, 236, 239, 0.8);
  transition: all 0.3s ease;
}

.modern-order-form:hover {
  box-shadow: 0 4px 16px rgba(102, 126, 234, 0.15);
  border-color: rgba(102, 126, 234, 0.3);
}

/* 现代化成功提示框样式 */
#alert_frame.alert-success {
  background: linear-gradient(135deg, #71D7A2 0%, #5ED1D7 100%) !important;
  border: none !important;
  border-radius: 12px !important;
  padding: 12px 16px !important;
  box-shadow: 0 4px 16px rgba(113, 215, 162, 0.3) !important;
  color: white !important;
  font-weight: 600 !important;
  font-size: 14px !important;
  border-left: 4px solid rgba(255, 255, 255, 0.3) !important;
}

.tab-pane {
  display: none;
}

.tab-pane.active {
  display: block;
}

/* 表单样式 */
.form-group {
  margin-bottom: 8px;
}

.form-control {
  padding: 6px 10px;
  max-width: 100%;
  margin: 1px 0;
  color: #454e59;
  border: 1px solid #dee2e6;
  border-radius: 4px;
  font-size: 14px;
  background: white;
  height: 32px;
}

.form-control:focus {
  border-color: #667eea;
  outline: none;
}

.input-group {
  position: relative;
  display: table;
  border-collapse: separate;
  width: 100%;
  margin-bottom: 8px;
}

.input-group-addon {
  padding: 6px 12px;
  font-size: 14px;
  font-weight: 500;
  line-height: 1;
  color: white;
  text-align: center;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border: 1px solid transparent;
  border-radius: 8px;
  white-space: nowrap;
  vertical-align: middle;
  display: table-cell;
  width: 1%;
  box-shadow: 0 2px 8px rgba(102, 126, 234, 0.3);
  transition: all 0.3s ease;
}

.input-group .form-control {
  position: relative;
  z-index: 2;
  float: left;
  width: 100%;
  margin-bottom: 0;
  display: table-cell;
  border-radius: 8px;
  border: 2px solid #dee2e6;
  background: white;
  transition: all 0.3s ease;
}

.input-group .form-control:focus {
  border-color: #667eea;
  outline: 0;
  box-shadow: 0 0 0 4px rgba(102, 126, 234, 0.15), 0 2px 8px rgba(102, 126, 234, 0.2);
  transform: translateY(-1px);
}

.input-group .form-control:not(:first-child):not(:last-child) {
  border-radius: 0;
}

.input-group-addon:first-child {
  border-top-right-radius: 0;
  border-bottom-right-radius: 0;
  border-right: 0;
  border-top-left-radius: 8px;
  border-bottom-left-radius: 8px;
}

.input-group .form-control:first-child,
.input-group-addon:first-child {
  border-top-right-radius: 0;
  border-bottom-right-radius: 0;
}

.input-group-addon:last-child {
  border-top-left-radius: 0;
  border-bottom-left-radius: 0;
  border-left: 0;
  border-top-right-radius: 8px;
  border-bottom-right-radius: 8px;
}

.input-group .form-control:last-child,
.input-group-addon:last-child {
  border-top-left-radius: 0;
  border-bottom-left-radius: 0;
}



/* 按钮样式 */
.btn {
  border-radius: 4px;
  padding: 6px 16px;
  font-size: 14px;
  border: none;
  height: 32px;
  line-height: 20px;
  text-align: center;
  display: inline-block;
  vertical-align: middle;
  cursor: pointer;
}

.btn-primary {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: none;
  box-shadow: 0 2px 8px rgba(102, 126, 234, 0.3);
  transition: all 0.3s ease;
  font-weight: 500;
}

.btn-primary:hover,
.btn-primary:focus {
  background: linear-gradient(135deg, #5a67d8 0%, #6a4c93 100%);
  color: white;
  transform: translateY(-2px);
  box-shadow: 0 4px 16px rgba(102, 126, 234, 0.4);
}

.btn-success {
  background: #28a745;
  color: white;
}

.btn-success:hover,
.btn-success:focus {
  background: #218838;
  color: white;
}

.btn-info {
  background: linear-gradient(135deg, #17a2b8 0%, #138496 100%);
  color: white;
  border: none;
  box-shadow: 0 2px 6px rgba(23, 162, 184, 0.3);
  transition: all 0.3s ease;
  font-weight: 500;
}

.btn-info:hover,
.btn-info:focus {
  background: linear-gradient(135deg, #138496 0%, #0f6674 100%);
  color: white;
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(23, 162, 184, 0.4);
}

.btn-warning {
  background: #ffc107;
  color: #212529;
}

.btn-warning:hover,
.btn-warning:focus {
  background: #e0a800;
  color: #212529;
}

.btn-danger {
  background: #dc3545;
  color: white;
}

.btn-danger:hover,
.btn-danger:focus {
  background: #c82333;
  color: white;
}

.btn-block {
  width: 100%;
  margin-bottom: 6px;
  display: block;
  box-sizing: border-box;
}

/* 确保立即购买按钮与input-group宽度一致 */
#submit_buy.btn-block {
  width: 100%;
  margin-left: 0;
  margin-right: 0;
  padding: 12px 16px;
  border-radius: 8px;
  font-size: 16px;
  font-weight: 600;
  letter-spacing: 0.5px;
}

#submit_buy.btn-block:active {
  transform: translateY(0);
}

.btn-group-justified .btn {
  margin: 0 3px;
}

.btn-group-justified .btn:first-child {
  margin-left: 0;
}

.btn-group-justified .btn:last-child {
  margin-right: 0;
}

/* 模态框样式 */
.modal {
  position: fixed;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  z-index: 1050;
  display: none;
  overflow: hidden;
  outline: 0;
  background: rgba(0, 0, 0, 0.5);
}

.modal[v-show="true"] {
  display: flex;
  align-items: center;
  justify-content: center;
}

.modal-dialog {
  position: relative;
  width: auto;
  margin: 15px;
  max-width: 500px;
}

.modal-content {
  position: relative;
  background-color: #fff;
  border: 1px solid #dee2e6;
  border-radius: 6px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
  background-clip: padding-box;
  outline: 0;
  overflow: hidden;
}

.modal-header {
  padding: 12px 15px;
  border-bottom: 1px solid #dee2e6;
}

.modal-body {
  position: relative;
  padding: 15px;
  max-height: 50vh;
  overflow-y: auto;
}

.modal-footer {
  padding: 12px 15px;
  text-align: right;
  border-top: 1px solid #dee2e6;
  background: #f8f9fa;
}

.modal-footer .btn {
  margin-left: 6px;
}

/* 表格样式 */
.table {
  width: 100%;
  max-width: 100%;
  margin-bottom: 12px;
  border-collapse: collapse;
}

.table > thead > tr > th,
.table > tbody > tr > th,
.table > tfoot > tr > th,
.table > thead > tr > td,
.table > tbody > tr > td,
.table > tfoot > tr > td {
  padding: 6px 8px;
  line-height: 1.4;
  vertical-align: middle;
  border-top: 1px solid #dee2e6;
  font-size: 14px;
  height: 36px;
}

.table > thead > tr > th {
  background: #667eea;
  color: white;
  font-weight: 500;
  text-align: center;
  border: none;
  height: 40px;
}

.table-striped > tbody > tr:nth-of-type(odd) {
  background-color: #f8f9fa;
}

.table-striped > tbody > tr:nth-of-type(even) {
  background-color: #ffffff;
}

.table-striped > tbody > tr:hover {
  background-color: #e9ecef;
}

.table-bordered {
  border: 1px solid #dee2e6;
  border-radius: 4px;
  overflow: hidden;
}

.table-bordered > thead > tr > th,
.table-bordered > tbody > tr > th,
.table-bordered > tfoot > tr > th,
.table-bordered > thead > tr > td,
.table-bordered > tbody > tr > td,
.table-bordered > tfoot > tr > td {
  border: 1px solid #dee2e6;
}

.table-responsive {
  border-radius: 4px;
  overflow: hidden;
}

/* 面板样式 */
.panel {
  margin-bottom: 12px;
  background-color: rgba(255, 255, 255, 0.9);
  border: 1px solid rgba(222, 226, 230, 0.8);
  border-radius: 6px;
  overflow: hidden;
}

.panel-primary > .panel-heading {
  color: #fff;
  background: #667eea;
  border: none;
}

.panel-heading {
  padding: 8px 15px;
  border-bottom: 1px solid #dee2e6;
  border-radius: 6px 6px 0 0;
  height: 40px;
  display: flex;
  align-items: center;
}

.panel-heading .panel-title {
  font-size: 16px;
  font-weight: 500;
  margin: 0;
}

.panel-body {
  padding: 12px;
}

/* 底部面板样式优化 */
.panel.panel-default .panel-body {
  text-align: center;
  font-size: 14px;
  color: #6c757d;
  line-height: 1.5;
}

.panel.panel-default .panel-body a {
  color: #667eea;
  text-decoration: none;
}

.panel.panel-default .panel-body a:hover {
  color: #5a67d8;
  text-decoration: underline;
}

/* 数据统计面板样式 */
.stats-panel {
  margin-bottom: 12px;
}

.stats-header {
  padding: 6px 12px;
  height: 32px;
  display: flex;
  align-items: center;
}

.stats-header .panel-title {
  font-size: 14px;
  font-weight: 500;
  display: flex;
  align-items: center;
  gap: 6px;
  color: white;
}

.stats-header i {
  font-size: 14px;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 1px;
  background: #dee2e6;
  padding: 0;
}

.stats-item {
  background: white;
  padding: 8px 4px;
  text-align: center;
  height: 52px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  position: relative;
  gap: 3px;
}

.stats-number {
  font-size: 16px;
  font-weight: 600;
  color: #667eea;
  line-height: 1;
}

.stats-unit {
  font-size: 11px;
  color: #667eea;
  display: inline;
  margin-left: 2px;
}

.stats-icon {
  position: absolute;
  top: 2px;
  right: 4px;
  font-size: 10px;
  color: #6c757d;
  opacity: 0.6;
}

.stats-label {
  font-size: 13px;
  color: #6c757d;
  line-height: 1;
}

/* 响应式调整 */
@media (max-width: 767px) {
  .stats-grid {
    grid-template-columns: repeat(2, 1fr);
  }

  .stats-item {
    height: 48px;
    padding: 6px 4px;
    gap: 2px;
  }

  .stats-number {
    font-size: 14px;
  }

  .stats-unit {
    font-size: 10px;
  }

  .stats-label {
    font-size: 12px;
  }

  .stats-icon {
    font-size: 9px;
  }
}

/* 简化动画效果 - 移除复杂动画 */

/* 响应式设计 */
@media (min-width: 768px) {
  .modal-dialog {
    width: 600px;
    margin: 30px auto;
  }
}

@media (min-width: 992px) {
  .modal-lg {
    width: 900px;
  }
}

/* 工具类 */
.text-center {
  text-align: center;
}

.text-right {
  text-align: right;
}

.text-left {
  text-align: left;
}

.pull-left {
  float: left;
}

.pull-right {
  float: right;
}

.clearfix:before,
.clearfix:after {
  content: " ";
  display: table;
}

.clearfix:after {
  clear: both;
}

.center-block {
  display: block;
  margin-left: auto;
  margin-right: auto;
}

/* 颜色类 */
.text-primary {
  color: #5ccdde;
}

.text-success {
  color: #afde5c;
}

.text-info {
  color: #5cafde;
}

.text-warning {
  color: #deb25c;
}

.text-danger {
  color: #de815c;
}

.text-muted {
  color: #999999;
}

/* 边框工具类 */
.border-t {
  border-top: 1px solid #e9e9e9;
}

.border-b {
  border-bottom: 1px solid #e9e9e9;
}

/* 图片样式 */
.img-circle {
  border-radius: 50%;
}

.img-thumbnail {
  display: inline-block;
  max-width: 100%;
  height: auto;
  padding: 4px;
  line-height: 1.42857143;
  background-color: #fff;
  border: 1px solid #ddd;
  border-radius: 4px;
  transition: all .2s ease-in-out;
}

/* 特殊效果 */
.onclick {
  cursor: pointer;
  touch-action: manipulation;
}

/* 隐藏类 */
.hidden-xs {
  display: block;
}

@media (max-width: 767px) {
  .hidden-xs {
    display: none;
  }
}

/* 按钮组 */
.btn-group-justified {
  display: table;
  width: 100%;
  table-layout: fixed;
  border-collapse: separate;
}

.btn-group-justified > .btn,
.btn-group-justified > .btn-group {
  display: table-cell;
  float: none;
  width: 1%;
}

.btn-group-justified > .btn-group .btn {
  width: 100%;
}

/* 列表组 */
.list-group {
  padding-left: 0;
  margin-bottom: 20px;
}

.list-group-item {
  position: relative;
  display: block;
  padding: 10px 15px;
  margin-bottom: -1px;
  background-color: #fff;
  border: 1px solid #ddd;
}

.list-group-item:first-child {
  border-top-left-radius: 4px;
  border-top-right-radius: 4px;
}

.list-group-item:last-child {
  margin-bottom: 0;
  border-bottom-right-radius: 4px;
  border-bottom-left-radius: 4px;
}

/* 标签样式 */
.label {
  display: inline-block;
  padding: 4px 8px;
  font-size: 12px;
  font-weight: 500;
  line-height: 1.2;
  color: #fff;
  text-align: center;
  white-space: nowrap;
  vertical-align: baseline;
  border-radius: 12px;
  height: 24px;
  display: inline-flex;
  align-items: center;
}

.label-primary {
  background: #667eea;
}

.label-success {
  background: #28a745;
}

.label-info {
  background: #17a2b8;
}

.label-warning {
  background: #ffc107;
  color: #212529;
}

.label-danger {
  background: #dc3545;
}

/* 徽章样式 */
.badge {
  display: inline-block;
  min-width: 10px;
  padding: 3px 7px;
  font-size: 12px;
  font-weight: bold;
  line-height: 1;
  color: #fff;
  text-align: center;
  white-space: nowrap;
  vertical-align: middle;
  background-color: #777;
  border-radius: 10px;
}

/* 进度条 */
.progress {
  height: 20px;
  margin-bottom: 20px;
  overflow: hidden;
  background-color: #f5f5f5;
  border-radius: 4px;
  box-shadow: inset 0 1px 2px rgba(0, 0, 0, .1);
}

.progress-bar {
  float: left;
  width: 0%;
  height: 100%;
  font-size: 12px;
  line-height: 20px;
  color: #fff;
  text-align: center;
  background-color: #337ab7;
  box-shadow: inset 0 -1px 0 rgba(0, 0, 0, .15);
  transition: width .6s ease;
}

/* 警告框 */
.alert {
  padding: 8px 12px;
  margin-bottom: 8px;
  border: 1px solid transparent;
  border-radius: 4px;
  font-size: 14px;
  min-height: 36px;
  display: flex;
  align-items: center;
}

.alert-success {
  color: #155724;
  background: #d4edda;
  border-color: #c3e6cb;
}

.alert-info {
  color: #0c5460;
  background: #d1ecf1;
  border-color: #bee5eb;
}

.alert-warning {
  color: #856404;
  background: #fff3cd;
  border-color: #ffeaa7;
}

.alert-danger {
  color: #721c24;
  background: #f8d7da;
  border-color: #f5c6cb;
}

/* 井样式 */
.well {
  min-height: 32px;
  padding: 8px 12px;
  margin-bottom: 8px;
  background: #f8f9fa;
  border: 1px solid #dee2e6;
  border-radius: 4px;
}

.well-sm {
  padding: 6px 8px;
  border-radius: 4px;
  min-height: 28px;
}

/* 关闭按钮 */
.close {
  float: right;
  font-size: 21px;
  font-weight: bold;
  line-height: 1;
  color: #000;
  text-shadow: 0 1px 0 #fff;
  filter: alpha(opacity=20);
  opacity: .2;
}

.close:hover,
.close:focus {
  color: #000;
  text-decoration: none;
  cursor: pointer;
  filter: alpha(opacity=50);
  opacity: .5;
}

/* 字体图标 */
.fa {
  display: inline-block;
  font: normal normal normal 14px/1 FontAwesome;
  font-size: inherit;
  text-rendering: auto;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.fa-spin {
  animation: fa-spin 2s infinite linear;
}

@keyframes fa-spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(359deg);
  }
}

/* 特定组件样式 */
.themed-background-flat {
  background-color: #f9f9f9;
}

.widget-icon {
  display: inline-block;
  width: 64px;
  height: 64px;
  line-height: 60px;
  margin: 5px;
  font-size: 28px;
  text-align: center;
  border-radius: 50%;
  background: rgba(0, 0, 0, .05);
}

.widget-heading {
  margin: 10px 0;
}

.themed-background-info {
  background-color: #5bc0de;
}

/* 表格定价样式 */
.table-pricing {
  background-color: #ffffff;
  text-align: center;
  border: 2px solid #ffffff;
  transition: all .15s ease-out;
}

.table-pricing:hover {
  border-color: #5ccdde;
  box-shadow: 0 0 20px rgba(0, 0, 0, .2);
}

/* 按钮效果 */
.btn-effect-ripple {
  position: relative;
  overflow: hidden;
}

.btn-ripple {
  position: absolute;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.6);
  transform: scale(0);
  animation: ripple 0.6s linear;
}

@keyframes ripple {
  to {
    transform: scale(4);
    opacity: 0;
  }
}

/* 响应式优化 */
@media (max-width: 767px) {
  .main-container {
    padding: 5px 0;
  }

  .col-xs-12 {
    width: 100%;
    padding: 0 8px;
  }

  .modern-btn-group {
    gap: 4px;
  }

  .btn-modern {
    padding: 4px;
    font-size: 13px;
    height: 40px;
  }

  .modern-nav-tabs .nav-link {
    padding: 6px 4px;
    font-size: 13px;
    height: 40px;
  }

  .modern-nav-tabs .nav-link i {
    font-size: 14px;
  }

  .tab-content {
    padding: 6px;
  }

  .modal-dialog {
    margin: 8px;
    width: calc(100% - 16px);
  }

  .modal-body {
    max-height: 40vh;
  }

  .logo-header {
    padding: 8px;
  }

  .logo-avatar {
    width: 60px;
    height: 60px;
  }

  .avatar-img {
    border-radius: 30px;
  }

  .brand-subtitle {
    font-size: 12px;
  }

  .form-control {
    height: 28px;
    padding: 4px 8px;
    font-size: 12px;
  }

  .input-group-addon {
    padding: 4px 8px;
    font-size: 12px;
    line-height: 1.5;
  }

  .btn {
    height: 28px;
    padding: 4px 12px;
  }

  .table > thead > tr > th,
  .table > tbody > tr > td {
    height: 32px;
    padding: 4px 6px;
  }
}

@media (min-width: 768px) {
  .col-sm-6 {
    width: 50%;
    float: left;
  }

  .col-sm-10 {
    width: 83.33333333%;
  }

  .modern-nav-tabs .nav-link {
    padding: 20px 15px;
  }
}

@media (min-width: 992px) {
  .col-md-8 {
    width: 66.66666667%;
  }

  .tab-content {
    padding: 12px 16px;
  }
}

@media (min-width: 1200px) {
  .col-lg-5 {
    width: 41.66666667%;
  }

  .main-container {
    padding: 30px 0;
  }
}

/* 基础样式 */
* {
  box-sizing: border-box;
}

*:focus {
  outline: 2px solid #667eea;
  outline-offset: 1px;
}

/* 客服信息卡片样式 */
.customer-service-card {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 8px;
  padding: 16px;
  margin-bottom: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(233, 236, 239, 0.8);
  display: flex;
  align-items: center;
  gap: 16px;
  transition: all 0.3s ease;
}

.customer-service-card:hover {
  box-shadow: 0 4px 16px rgba(102, 126, 234, 0.15);
  border-color: rgba(102, 126, 234, 0.3);
}

.service-avatar {
  position: relative;
  flex-shrink: 0;
}

.avatar-image {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  border: 3px solid #667eea;
  object-fit: cover;
  transition: transform 0.3s ease;
}

.avatar-image:hover {
  transform: scale(1.05);
}

.online-status {
  position: absolute;
  bottom: 2px;
  right: 2px;
  width: 16px;
  height: 16px;
  background: #28a745;
  border-radius: 50%;
  border: 2px solid white;
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0% { opacity: 1; }
  50% { opacity: 0.6; }
  100% { opacity: 1; }
}

.service-info {
  flex: 1;
  min-width: 0;
}

.service-title {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 8px;
  font-size: 16px;
  font-weight: 600;
  color: #333;
}

.service-title i {
  color: #667eea;
  font-size: 18px;
}

.service-details {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.contact-item {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 14px;
  color: #667eea;
  font-weight: 500;
}

.contact-item i {
  font-size: 16px;
}

.service-desc {
  font-size: 13px;
  color: #6c757d;
  margin-top: 4px;
}

.service-action {
  flex-shrink: 0;
}

.contact-btn {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 4px;
  padding: 12px 16px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  text-decoration: none;
  border-radius: 8px;
  font-size: 13px;
  font-weight: 500;
  transition: all 0.3s ease;
  min-width: 80px;
  box-shadow: 0 2px 8px rgba(102, 126, 234, 0.3);
}

.contact-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 16px rgba(102, 126, 234, 0.4);
  color: white;
  text-decoration: none;
}

.contact-btn i {
  font-size: 18px;
}

/* 响应式设计 */
@media (max-width: 767px) {
  .customer-service-card {
    padding: 12px;
    gap: 12px;
  }

  .avatar-image {
    width: 50px;
    height: 50px;
  }

  .online-status {
    width: 14px;
    height: 14px;
  }

  .service-title {
    font-size: 15px;
  }

  .contact-item {
    font-size: 13px;
  }

  .service-desc {
    font-size: 12px;
  }

  .contact-btn {
    padding: 10px 12px;
    font-size: 12px;
    min-width: 70px;
  }

  .contact-btn i {
    font-size: 16px;
  }
}

/* 现代化搜索框样式 */
.search-container {
  margin-bottom: 8px;
}

.search-input-wrapper {
  position: relative;
  display: flex;
  align-items: center;
  background: white;
  border: 2px solid #dee2e6;
  border-radius: 12px;
  padding: 4px;
  transition: all 0.3s ease;
  margin-bottom: 8px;
}

.search-input-wrapper:focus-within {
  border-color: #667eea;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.search-icon {
  padding: 0 12px;
  color: #6c757d;
  font-size: 16px;
}

.modern-search-input {
  flex: 1;
  border: none;
  outline: none;
  padding: 12px 8px;
  font-size: 14px;
  background: transparent;
  color: #333;
}

.modern-search-input::placeholder {
  color: #6c757d;
}



.modern-search-btn {
  width: 100%;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: none;
  border-radius: 8px;
  padding: 12px 16px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  box-shadow: 0 2px 8px rgba(102, 126, 234, 0.3);
}

.modern-search-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 16px rgba(102, 126, 234, 0.4);
}

.modern-search-btn:active {
  transform: translateY(0);
}

/* 查询结果样式 */
.query-results {
  margin-top: 8px;
}



.order-cards {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.order-card {
  background: white;
  border-radius: 12px;
  border: 1px solid #dee2e6;
  overflow: hidden;
  transition: all 0.3s ease;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.order-card:hover {
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
  border-color: rgba(102, 126, 234, 0.3);
}

.order-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px;
  background: #f8f9fa;
  border-bottom: 1px solid #dee2e6;
}

.order-id {
  display: flex;
  align-items: center;
  gap: 8px;
  font-weight: 600;
  color: #333;
}

.order-id i {
  color: #667eea;
}

.order-status {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 6px 12px;
  border-radius: 20px;
  font-size: 13px;
  font-weight: 500;
}

.status-completed {
  background: rgba(40, 167, 69, 0.1);
  color: #28a745;
  border: 1px solid rgba(40, 167, 69, 0.2);
}

.status-processing {
  background: rgba(255, 193, 7, 0.1);
  color: #ffc107;
  border: 1px solid rgba(255, 193, 7, 0.2);
}

.status-pending {
  background: rgba(108, 117, 125, 0.1);
  color: #6c757d;
  border: 1px solid rgba(108, 117, 125, 0.2);
}

.status-failed {
  background: rgba(220, 53, 69, 0.1);
  color: #dc3545;
  border: 1px solid rgba(220, 53, 69, 0.2);
}

.order-content {
  padding: 16px;
}

.order-info {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.info-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.info-row {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 16px;
}

.info-item label {
  font-size: 13px;
  color: #6c757d;
  font-weight: 500;
  margin: 0;
}

.info-item span {
  font-size: 14px;
  color: #333;
  font-weight: 500;
}

.amount {
  color: #667eea !important;
  font-weight: 600 !important;
}



/* 空状态样式 */
.empty-state {
  text-align: center;
  padding: 60px 20px;
  background: white;
  border-radius: 12px;
  border: 1px solid #dee2e6;
}

.empty-icon {
  font-size: 48px;
  color: #dee2e6;
  margin-bottom: 16px;
}

.empty-text h4 {
  margin: 0 0 8px 0;
  color: #6c757d;
  font-size: 18px;
}

.empty-text p {
  margin: 0;
  color: #6c757d;
  font-size: 14px;
}

/* 搜索和结果区域响应式设计 */
@media (max-width: 767px) {
  .search-input-wrapper {
    border-radius: 8px;
    padding: 2px;
  }

  .modern-search-input {
    padding: 10px 6px;
    font-size: 13px;
  }

  .search-icon {
    padding: 0 8px;
    font-size: 14px;
  }

  .modern-search-btn {
    padding: 10px 12px;
    font-size: 13px;
  }



  .order-header {
    flex-direction: column;
    gap: 12px;
    align-items: flex-start;
    padding: 12px;
  }

  .order-status {
    align-self: flex-end;
  }

  .order-content {
    padding: 12px;
  }

  .info-row {
    grid-template-columns: 1fr;
    gap: 12px;
  }



  .empty-state {
    padding: 40px 16px;
  }

  .empty-icon {
    font-size: 36px;
  }

  .empty-text h4 {
    font-size: 16px;
  }

  .empty-text p {
    font-size: 13px;
  }
}
</style>
